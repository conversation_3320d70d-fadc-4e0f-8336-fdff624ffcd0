/**
 * Enhanced Interactive Games Component
 * Functional educational games with source selection and topic prompts
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Gamepad2,
  Brain,
  Globe,
  FileText,
  Sparkles,
  Target,
  Puzzle,
  Zap,
  BookOpen,
  Trophy,
  Play,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Lightbulb,
  Timer,
  Star,
  RotateCcw,
  Settings
} from "lucide-react";
import { ResearchDocument } from '../types';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';
import { SourceSelector, SourceType } from './SourceSelector';

interface EnhancedInteractiveGamesProps {
  uploadedDocuments?: ResearchDocument[];
}

interface GameConfig {
  type: string;
  topic: string;
  source: SourceType;
  educationLevel: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface GameQuestion {
  id: string;
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation?: string;
  hint?: string;
  points: number;
}

interface GameState {
  isActive: boolean;
  currentQuestion: number;
  score: number;
  totalQuestions: number;
  questions: GameQuestion[];
  timeRemaining: number;
  gameType: string;
  isComplete: boolean;
}

const gameTypes = [
  {
    id: 'quiz-challenge',
    name: 'Quiz Challenge',
    description: 'Fast-paced multiple choice questions',
    icon: Target,
    color: 'bg-blue-500',
    difficulty: 'Easy to Hard'
  },
  {
    id: 'word-match',
    name: 'Word Match',
    description: 'Match terms with their definitions',
    icon: Puzzle,
    color: 'bg-green-500',
    difficulty: 'Medium'
  },
  {
    id: 'true-false-rush',
    name: 'True/False Rush',
    description: 'Quick true or false questions',
    icon: Zap,
    color: 'bg-yellow-500',
    difficulty: 'Easy'
  },
  {
    id: 'concept-builder',
    name: 'Concept Builder',
    description: 'Build understanding step by step',
    icon: BookOpen,
    color: 'bg-purple-500',
    difficulty: 'Medium to Hard'
  },
  {
    id: 'memory-master',
    name: 'Memory Master',
    description: 'Remember and recall key facts',
    icon: Brain,
    color: 'bg-indigo-500',
    difficulty: 'Hard'
  },
  {
    id: 'speed-learning',
    name: 'Speed Learning',
    description: 'Learn concepts under time pressure',
    icon: Trophy,
    color: 'bg-red-500',
    difficulty: 'Very Hard'
  }
];



const educationLevels = [
  { value: 'elementary', label: 'Elementary School' },
  { value: 'middle-school', label: 'Middle School' },
  { value: 'high-school', label: 'High School' },
  { value: 'undergraduate', label: 'Undergraduate' },
  { value: 'graduate', label: 'Graduate' }
];

const topicSuggestions = [
  'Solar System and Planets',
  'Ancient Civilizations',
  'Basic Algebra',
  'Animal Kingdom',
  'Chemical Elements',
  'World Geography',
  'Human Body Systems',
  'Famous Literature',
  'Mathematical Formulas',
  'Historical Events'
];

export function EnhancedInteractiveGames({ uploadedDocuments = [] }: EnhancedInteractiveGamesProps) {
  const [selectedGame, setSelectedGame] = useState<string | null>(null);
  const [gameConfig, setGameConfig] = useState<GameConfig>({
    type: '',
    topic: '',
    source: 'ai',
    educationLevel: 'high-school',
    difficulty: 'medium'
  });
  const [gameState, setGameState] = useState<GameState>({
    isActive: false,
    currentQuestion: 0,
    score: 0,
    totalQuestions: 0,
    questions: [],
    timeRemaining: 0,
    gameType: '',
    isComplete: false
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [showConfig, setShowConfig] = useState(false);

  const handleGameSelect = (gameId: string) => {
    setSelectedGame(gameId);
    setGameConfig(prev => ({ ...prev, type: gameId }));
    setShowConfig(true);
  };

  const handleTopicSuggestionClick = (topic: string) => {
    setGameConfig(prev => ({ ...prev, topic }));
  };

  const generateGameContent = async () => {
    if (!gameConfig.topic.trim()) {
      toast.error('Please enter a topic for the game');
      return;
    }

    if (gameConfig.source === 'documents' && uploadedDocuments.length === 0) {
      toast.error('Please upload documents to use them as a source');
      return;
    }

    setIsGenerating(true);

    try {
      // Generate questions based on game type and configuration
      const questions = await integratedTutorService.generatePracticeQuestions(
        gameConfig.topic,
        gameConfig.educationLevel,
        getQuestionCountForGame(gameConfig.type),
        gameConfig.difficulty
      );

      if (questions.length === 0) {
        throw new Error('No questions were generated. Please try again.');
      }

      // Convert to game questions format
      const gameQuestions: GameQuestion[] = questions.map((q, index) => ({
        id: `q${index + 1}`,
        question: q.question,
        options: q.options || [],
        correctAnswer: q.correctAnswer || '',
        explanation: q.explanation,
        hint: `Think about the key concepts related to ${gameConfig.topic}`,
        points: getPointsForDifficulty(gameConfig.difficulty)
      }));

      // Initialize game state
      setGameState({
        isActive: true,
        currentQuestion: 0,
        score: 0,
        totalQuestions: gameQuestions.length,
        questions: gameQuestions,
        timeRemaining: getTimeForGame(gameConfig.type),
        gameType: gameConfig.type,
        isComplete: false
      });

      setShowConfig(false);
      toast.success('Game started! Good luck!');

    } catch (error) {
      console.error('Game generation failed:', error);
      toast.error(`Failed to generate game: ${error.message}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const getQuestionCountForGame = (gameType: string): number => {
    switch (gameType) {
      case 'quiz-challenge': return 15;
      case 'word-match': return 10;
      case 'true-false-rush': return 20;
      case 'concept-builder': return 8;
      case 'memory-master': return 12;
      case 'speed-learning': return 25;
      default: return 10;
    }
  };

  const getPointsForDifficulty = (difficulty: string): number => {
    switch (difficulty) {
      case 'easy': return 10;
      case 'medium': return 20;
      case 'hard': return 30;
      default: return 20;
    }
  };

  const getTimeForGame = (gameType: string): number => {
    switch (gameType) {
      case 'quiz-challenge': return 300; // 5 minutes
      case 'word-match': return 240; // 4 minutes
      case 'true-false-rush': return 180; // 3 minutes
      case 'concept-builder': return 600; // 10 minutes
      case 'memory-master': return 360; // 6 minutes
      case 'speed-learning': return 120; // 2 minutes
      default: return 300;
    }
  };

  const handleAnswerSubmit = (selectedAnswer: string) => {
    const currentQ = gameState.questions[gameState.currentQuestion];
    const isCorrect = selectedAnswer === currentQ.correctAnswer;
    
    if (isCorrect) {
      setGameState(prev => ({
        ...prev,
        score: prev.score + currentQ.points
      }));
      toast.success('Correct! +' + currentQ.points + ' points');
    } else {
      toast.error('Incorrect. The correct answer was: ' + currentQ.correctAnswer);
    }

    // Move to next question or complete game
    setTimeout(() => {
      if (gameState.currentQuestion + 1 >= gameState.totalQuestions) {
        setGameState(prev => ({ ...prev, isComplete: true, isActive: false }));
      } else {
        setGameState(prev => ({
          ...prev,
          currentQuestion: prev.currentQuestion + 1
        }));
      }

  // Active game screen
  if (gameState.isActive) {
    const currentQuestion = gameState.questions[gameState.currentQuestion];
    const progress = ((gameState.currentQuestion + 1) / gameState.totalQuestions) * 100;

    return (
      <div className="max-w-4xl mx-auto p-6">
        {/* Game Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={resetGame}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Exit Game
            </Button>
            <div>
              <h2 className="text-xl font-bold">{gameTypes.find(g => g.id === gameState.gameType)?.name}</h2>
              <p className="text-sm text-gray-600">{gameConfig.topic}</p>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{gameState.score}</div>
              <div className="text-xs text-gray-600">Score</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{gameState.currentQuestion + 1}/{gameState.totalQuestions}</div>
              <div className="text-xs text-gray-600">Question</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">{Math.floor(gameState.timeRemaining / 60)}:{(gameState.timeRemaining % 60).toString().padStart(2, '0')}</div>
              <div className="text-xs text-gray-600">Time Left</div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <Progress value={progress} className="mb-8" />

        {/* Question Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">
              Question {gameState.currentQuestion + 1}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg mb-6">{currentQuestion.question}</p>

            {currentQuestion.options && currentQuestion.options.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentQuestion.options.map((option, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    className="p-4 h-auto text-left justify-start hover:bg-blue-50 hover:border-blue-300"
                    onClick={() => handleAnswerSubmit(option)}
                  >
                    <span className="font-medium mr-3">{String.fromCharCode(65 + index)}.</span>
                    {option}
                  </Button>
                ))}
              </div>
            )}

            {currentQuestion.hint && (
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Lightbulb className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium text-yellow-800">Hint:</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">{currentQuestion.hint}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Game configuration screen
  if (showConfig && selectedGame) {
    const gameType = gameTypes.find(g => g.id === selectedGame);

    return (
      <div className="max-w-3xl mx-auto space-y-6">
        <div className="flex items-center space-x-4 mb-6">
          <Button variant="outline" onClick={backToGameSelection}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h2 className="text-2xl font-bold">Configure {gameType?.name}</h2>
            <p className="text-gray-600">{gameType?.description}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Topic Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lightbulb className="w-5 h-5 text-yellow-500" />
                <span>Game Topic</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="gameTopic">What topic do you want to learn about?</Label>
                <Textarea
                  id="gameTopic"
                  placeholder="Enter the topic for your game..."
                  value={gameConfig.topic}
                  onChange={(e) => setGameConfig(prev => ({ ...prev, topic: e.target.value }))}
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label className="text-sm text-gray-600">Suggested Topics</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {topicSuggestions.map((topic) => (
                    <Badge
                      key={topic}
                      variant="outline"
                      className="cursor-pointer hover:bg-blue-50 hover:border-blue-300"
                      onClick={() => handleTopicSuggestionClick(topic)}
                    >
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Source & Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5 text-gray-500" />
                <span>Game Settings</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <SourceSelector
                selectedSource={gameConfig.source}
                onSourceChange={(source) => setGameConfig(prev => ({ ...prev, source }))}
                uploadedDocuments={uploadedDocuments}
                showMixed={false}
                variant="buttons"
                title="Knowledge Source"
                description="Choose where to get game content from"
              />

              <div>
                <Label>Education Level</Label>
                <Select value={gameConfig.educationLevel} onValueChange={(value) => setGameConfig(prev => ({ ...prev, educationLevel: value }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {educationLevels.map(level => (
                      <SelectItem key={level.value} value={level.value}>
                        {level.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Difficulty</Label>
                <Select value={gameConfig.difficulty} onValueChange={(value) => setGameConfig(prev => ({ ...prev, difficulty: value as any }))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Start Game Button */}
        <Card>
          <CardContent className="p-6">
            <Button
              onClick={generateGameContent}
              disabled={!gameConfig.topic.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white py-3 text-lg font-medium"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Game...
                </>
              ) : (
                <>
                  <Play className="w-5 h-5 mr-2" />
                  Start Game
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Game selection screen
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Interactive Learning Games</h1>
        <p className="text-gray-600">Choose a game type and start learning through play!</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {gameTypes.map((game) => (
          <Card
            key={game.id}
            className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 bg-white border-gray-200"
            onClick={() => handleGameSelect(game.id)}
          >
            <CardContent className="p-6">
              <div className="flex items-center space-x-4 mb-4">
                <div className={`p-3 rounded-lg ${game.color}`}>
                  <game.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-lg">{game.name}</h3>
                  <p className="text-sm text-gray-600">{game.description}</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  {game.difficulty}
                </Badge>
                <Button size="sm" className="bg-gradient-to-r from-blue-600 to-indigo-600">
                  <Play className="w-4 h-4 mr-1" />
                  Play
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
    }, 2000);
  };

  const resetGame = () => {
    setGameState({
      isActive: false,
      currentQuestion: 0,
      score: 0,
      totalQuestions: 0,
      questions: [],
      timeRemaining: 0,
      gameType: '',
      isComplete: false
    });
    setSelectedGame(null);
    setShowConfig(false);
  };

  const backToGameSelection = () => {
    setShowConfig(false);
    setSelectedGame(null);
  };

  // Game completion screen
  if (gameState.isComplete) {
    const percentage = Math.round((gameState.score / (gameState.totalQuestions * getPointsForDifficulty(gameConfig.difficulty))) * 100);
    
    return (
      <div className="max-w-2xl mx-auto p-8">
        <Card className="bg-gradient-to-br from-green-50 to-blue-100 border-green-200">
          <CardContent className="p-8 text-center">
            <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-blue-600 rounded-full mx-auto mb-6">
              <Trophy className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Game Complete!</h2>
            <p className="text-gray-600 mb-6">Great job on completing the {gameTypes.find(g => g.id === gameState.gameType)?.name}!</p>
            
            <div className="grid grid-cols-3 gap-4 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{gameState.score}</div>
                <div className="text-sm text-gray-600">Total Score</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{percentage}%</div>
                <div className="text-sm text-gray-600">Accuracy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{gameState.totalQuestions}</div>
                <div className="text-sm text-gray-600">Questions</div>
              </div>
            </div>

            <div className="flex space-x-4 justify-center">
              <Button onClick={resetGame} variant="outline">
                <RotateCcw className="w-4 h-4 mr-2" />
                Play Again
              </Button>
              <Button onClick={() => setSelectedGame(null)} className="bg-gradient-to-r from-blue-600 to-indigo-600">
                <Gamepad2 className="w-4 h-4 mr-2" />
                Choose New Game
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
