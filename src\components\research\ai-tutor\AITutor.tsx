/**
 * AI Tutor - Main Component
 * Provides personalized AI tutoring experiences with flexible learning modes
 */

import React, { useState, useEffect } from 'react';
import { FlexibleTutorChat } from './components/FlexibleTutorChat';
import { DocumentUploader } from './components/TutorHero';
import { ResearchSettings } from './components/ResearchSettings';
import { QuizGenerator } from './components/QuizGenerator';
import { QuizInterface } from './components/QuizInterface';
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  GraduationCap,
  MessageSquare,
  BookOpen,
  Settings,
  Brain,
  FileText,
  Search,
  Trophy,
  Target
} from "lucide-react";
import { Quiz, ResearchDocument } from './types';
import { documentService } from './services/document.service';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

export function AITutor() {
  const [activeTab, setActiveTab] = useState('chat');
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [showQuizInterface, setShowQuizInterface] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<ResearchDocument[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<ResearchDocument | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState({
    preferredModel: 'gemini-2.5-flash',
    educationLevel: 'intermediate', // Changed from 'high-school' to research-focused
    useCodeExecution: false,
    useWebSearch: true,
    theme: 'light'
  });

  // Load user documents on component mount
  useEffect(() => {
    loadUserDocuments();
  }, []);

  const loadUserDocuments = async () => {
    try {
      setIsLoading(true);
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        const documents = await documentService.getUserDocuments(user.id);
        setUploadedDocuments(documents);
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewConversation = () => {
    // Reset any conversation state if needed
    console.log('Starting new conversation');
  };

  const handleDocumentUploaded = async (document: ResearchDocument) => {
    try {
      console.log('Document uploaded:', document.title);
      setUploadedDocuments(prev => [...prev, document]);
      setSelectedDocument(document);
      toast.success(`Document "${document.title}" uploaded successfully!`);
    } catch (error) {
      console.error('Failed to handle document upload:', error);
      toast.error('Failed to process uploaded document');
    }
  };

  const handleDocumentSelected = async (document: ResearchDocument) => {
    try {
      console.log('Document selected:', document.title);
      setSelectedDocument(document);
    } catch (error) {
      console.error('Failed to select document:', error);
      toast.error('Failed to select document');
    }
  };

  const handleUpdateSettings = (newSettings: any) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const handleQuizGenerated = (quiz: Quiz) => {
    setCurrentQuiz(quiz);
    setShowQuizInterface(true);
  };

  const handleQuizComplete = (results: any) => {
    console.log('Quiz completed with results:', results);
    setShowQuizInterface(false);
    setCurrentQuiz(null);
    // Could save results to database here
  };

  const handleQuizExit = () => {
    setShowQuizInterface(false);
    setCurrentQuiz(null);
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">AI Tutor</h1>
                <p className="text-sm text-gray-500">Your Personal Learning Assistant</p>
              </div>
            </div>

            {/* Navigation Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-auto">
              <TabsList>
                <TabsTrigger value="chat" className="flex items-center space-x-2">
                  <MessageSquare className="w-4 h-4" />
                  <span>Chat</span>
                </TabsTrigger>
                <TabsTrigger value="quiz" className="flex items-center space-x-2">
                  <Trophy className="w-4 h-4" />
                  <span>Quiz</span>
                </TabsTrigger>
                <TabsTrigger value="documents" className="flex items-center space-x-2">
                  <FileText className="w-4 h-4" />
                  <span>Documents</span>
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>Settings</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="h-[calc(100vh-4rem)]">
        {showQuizInterface && currentQuiz ? (
          <QuizInterface
            quiz={currentQuiz}
            onComplete={handleQuizComplete}
            onExit={handleQuizExit}
          />
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <TabsContent value="chat" className="h-full m-0 p-0">
              <FlexibleTutorChat onNewConversation={handleNewConversation} />
            </TabsContent>

            <TabsContent value="quiz" className="h-full m-0 p-4 overflow-y-auto">
              <QuizGenerator
                onQuizGenerated={handleQuizGenerated}
                documentContent={selectedDocument?.content}
                documentTitle={selectedDocument?.title}
              />
            </TabsContent>

            <TabsContent value="documents" className="h-full m-0 p-4">
              <div className="max-w-7xl mx-auto">
                <DocumentUploader
                  onDocumentUploaded={handleDocumentUploaded}
                  onDocumentSelected={handleDocumentSelected}
                  documents={uploadedDocuments}
                  isLoading={isLoading}
                />
              </div>
            </TabsContent>

            <TabsContent value="settings" className="h-full m-0 p-4">
              <div className="max-w-7xl mx-auto">
                <ResearchSettings
                  settings={settings}
                  onUpdateSettings={handleUpdateSettings}
                  onBack={() => setActiveTab('chat')}
                />
              </div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}
