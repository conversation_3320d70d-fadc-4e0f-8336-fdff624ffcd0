/**
 * Enhanced AI Tutor Chat Component
 * Modern chat interface with streaming responses, source selection, and beautiful UI
 */

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Brain, 
  Send, 
  Loader2, 
  Search, 
  Code,
  BookOpen,
  Lightbulb,
  Sparkles,
  Bot,
  User,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RotateCcw,
  Settings,
  Globe,
  Database,
  Zap,
  MessageSquare,
  FileText
} from "lucide-react";
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';
import { ResearchDocument } from '../types';
import { SourceSelector, SourceType } from './SourceSelector';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
  codeOutput?: string;
  visualizations?: any[];
  isStreaming?: boolean;
}

interface EnhancedTutorChatProps {
  uploadedDocuments?: ResearchDocument[];
  onNewConversation?: () => void;
}

const educationLevels = [
  { value: 'elementary', label: 'Elementary School' },
  { value: 'middle-school', label: 'Middle School' },
  { value: 'high-school', label: 'High School' },
  { value: 'undergraduate', label: 'Undergraduate' },
  { value: 'graduate', label: 'Graduate' },
  { value: 'professional', label: 'Professional' }
];



const suggestions = [
  { text: "Explain quantum mechanics in simple terms", icon: Lightbulb, category: "Physics" },
  { text: "Help me understand calculus derivatives", icon: Brain, category: "Mathematics" },
  { text: "What is photosynthesis and how does it work?", icon: Sparkles, category: "Biology" },
  { text: "Explain the causes of World War I", icon: BookOpen, category: "History" },
  { text: "How do I solve quadratic equations?", icon: Zap, category: "Mathematics" },
  { text: "What is machine learning?", icon: Code, category: "Computer Science" }
];

export function EnhancedTutorChat({ uploadedDocuments = [], onNewConversation }: EnhancedTutorChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [educationLevel, setEducationLevel] = useState('high-school');
  const [selectedSource, setSelectedSource] = useState<SourceType>('ai');
  const [isLoading, setIsLoading] = useState(false);
  const [useCodeExecution, setUseCodeExecution] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (shouldAutoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10;
      setShouldAutoScroll(isAtBottom);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, shouldAutoScroll]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);
    setShouldAutoScroll(true);

    // Create assistant message placeholder
    const assistantMessage: Message = {
      id: (Date.now() + 1).toString(),
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isStreaming: true
    };

    setMessages(prev => [...prev, assistantMessage]);

    try {
      await generateResponse(assistantMessage.id, userMessage.content);
    } catch (error) {
      console.error('Error generating response:', error);
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? { ...msg, content: 'Sorry, I encountered an error. Please try again.', isStreaming: false }
          : msg
      ));
      toast.error('Failed to generate response. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const generateResponse = async (messageId: string, userQuery: string) => {
    try {
      const serviceStatus = integratedTutorService.getServiceStatus();

      if (!serviceStatus.gemini) {
        const fallbackResponse = `I apologize, but the AI service is currently unavailable.

**To enable AI tutoring, please:**

1. **Set up your Gemini API key:**
   - Get an API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Add it to your environment variables as \`VITE_GEMINI_API_KEY\`

2. **Optional - Enable web search:**
   - Get a Tavily API key from [Tavily](https://tavily.com)
   - Add it as \`VITE_TAVILY_API_KEY\`

Once configured, I'll be able to help you learn any topic with personalized explanations!`;

        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? { ...msg, content: fallbackResponse, isStreaming: false }
            : msg
        ));
        return;
      }

      // Determine options based on selected source
      const options = {
        educationLevel,
        useCodeExecution,
        useWebSearch: selectedSource === 'web',
        useRAG: selectedSource === 'documents' && uploadedDocuments.length > 0
      };

      const response = await integratedTutorService.generateResponse(
        userQuery,
        options,
        messages.filter(m => m.role === 'user' || m.role === 'assistant').map(m => ({
          role: m.role,
          content: m.content
        })),
        (chunk: string) => {
          setMessages(prev => prev.map(msg =>
            msg.id === messageId
              ? { ...msg, content: msg.content + chunk }
              : msg
          ));
        }
      );

      // Update with final response data
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              content: response.content,
              sources: response.sources,
              codeOutput: response.codeOutput,
              visualizations: response.visualizations,
              isStreaming: false
            }
          : msg
      ));

    } catch (error) {
      console.error('Response generation failed:', error);
      throw error;
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
    textareaRef.current?.focus();
  };

  const startNewConversation = () => {
    setMessages([]);
    setInputValue('');
    onNewConversation?.();
    toast.success('New conversation started');
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Message copied to clipboard');
  };

  return (
    <div className="flex flex-col h-full max-w-6xl mx-auto bg-gradient-to-br from-white to-blue-50/30 rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl shadow-lg">
            <Brain className="w-7 h-7 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              AI Tutor Chat
            </h1>
            <p className="text-sm text-gray-600">Your intelligent learning companion</p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Select value={educationLevel} onValueChange={setEducationLevel}>
            <SelectTrigger className="w-44 bg-white/80 border-gray-200/50 shadow-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-white/95 backdrop-blur-sm border-gray-200/50">
              {educationLevels.map(level => (
                <SelectItem key={level.value} value={level.value}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {messages.length > 0 && (
            <Button 
              variant="outline" 
              onClick={startNewConversation}
              className="bg-white/80 border-gray-200/50 hover:bg-white shadow-sm"
            >
              <RotateCcw className="w-4 h-4 mr-2" />
              New Chat
            </Button>
          )}
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-6"
        onScroll={handleScroll}
      >
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <div className="flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mx-auto mb-6 shadow-lg">
              <Brain className="w-10 h-10 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-3">
              What would you like to learn today?
            </h2>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
              Ask me anything! I can help with any subject at your education level using various knowledge sources.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-5xl mx-auto">
              {suggestions.map((suggestion, index) => (
                <Card
                  key={index}
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 bg-white/80 backdrop-blur-sm border-gray-200/50"
                  onClick={() => handleSuggestionClick(suggestion.text)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
                        <suggestion.icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">{suggestion.text}</p>
                        <Badge variant="secondary" className="text-xs mt-1 bg-blue-100 text-blue-700">
                          {suggestion.category}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-4xl ${
                  message.role === 'user'
                    ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white'
                    : 'bg-white/80 backdrop-blur-sm border border-gray-200/50'
                } rounded-2xl p-6 shadow-lg`}>

                  {/* Message Header */}
                  <div className="flex items-center space-x-3 mb-3">
                    <div className={`p-2 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-white/20'
                        : 'bg-gradient-to-r from-blue-500 to-indigo-600'
                    }`}>
                      {message.role === 'user' ? (
                        <User className="w-4 h-4 text-white" />
                      ) : (
                        <Bot className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <span className={`font-medium ${
                      message.role === 'user' ? 'text-white' : 'text-gray-900'
                    }`}>
                      {message.role === 'user' ? 'You' : 'AI Tutor'}
                    </span>
                    {message.isStreaming && (
                      <div className="flex items-center space-x-2">
                        <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                        <span className="text-xs text-gray-500">Thinking...</span>
                      </div>
                    )}
                  </div>

                  {/* Message Content */}
                  {message.role === 'assistant' ? (
                    <div className="prose prose-sm max-w-none">
                      <ReactMarkdown
                        components={{
                          code({ node, inline, className, children, ...props }) {
                            const match = /language-(\w+)/.exec(className || '');
                            return !inline && match ? (
                              <SyntaxHighlighter
                                style={tomorrow}
                                language={match[1]}
                                PreTag="div"
                                className="rounded-lg"
                                {...props}
                              >
                                {String(children).replace(/\n$/, '')}
                              </SyntaxHighlighter>
                            ) : (
                              <code className={`${className} bg-gray-100 px-1 py-0.5 rounded text-sm`} {...props}>
                                {children}
                              </code>
                            );
                          }
                        }}
                      >
                        {message.content}
                      </ReactMarkdown>

                      {/* Sources */}
                      {message.sources && message.sources.length > 0 && (
                        <div className="mt-6 pt-4 border-t border-gray-200">
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            <Globe className="w-4 h-4 mr-2" />
                            Sources:
                          </h4>
                          <div className="space-y-2">
                            {message.sources.map((source, index) => (
                              <div key={index} className="text-sm bg-blue-50 p-3 rounded-lg">
                                <a
                                  href={source.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:underline font-medium"
                                >
                                  {source.title}
                                </a>
                                <p className="text-gray-600 text-xs mt-1">{source.snippet}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Code Output */}
                      {message.codeOutput && (
                        <div className="mt-6 pt-4 border-t border-gray-200">
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                            <Code className="w-4 h-4 mr-2" />
                            Code Output:
                          </h4>
                          <pre className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto">
                            {message.codeOutput}
                          </pre>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-white">{message.content}</p>
                  )}

                  {/* Message Actions */}
                  {message.role === 'assistant' && !message.isStreaming && (
                    <div className="flex items-center space-x-2 mt-4 pt-3 border-t border-gray-200">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyMessage(message.content)}
                        className="text-gray-500 hover:text-gray-700"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 hover:text-green-600"
                      >
                        <ThumbsUp className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-500 hover:text-red-600"
                      >
                        <ThumbsDown className="w-4 h-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200/50 p-6 bg-white/80 backdrop-blur-sm">
        <form onSubmit={handleSubmit} className="flex space-x-4">
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask me anything about any subject..."
              className="min-h-[60px] max-h-32 resize-none bg-white/80 border-gray-200/50 focus:border-blue-400 focus:ring-blue-400/20 shadow-sm rounded-xl"
              disabled={isLoading}
            />
            {selectedSource === 'documents' && uploadedDocuments.length === 0 && (
              <p className="text-xs text-amber-600 mt-1">
                Upload documents to use them as a knowledge source
              </p>
            )}
          </div>
          <Button
            type="submit"
            disabled={!inputValue.trim() || isLoading}
            className="px-8 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg rounded-xl h-auto"
          >
            {isLoading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Send className="w-5 h-5" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}

      {/* Source Selection */}
      <div className="p-4 border-b border-gray-200/50 bg-gradient-to-r from-gray-50/80 to-blue-50/50">
        <div className="flex items-center justify-between">
          <SourceSelector
            selectedSource={selectedSource}
            onSourceChange={setSelectedSource}
            uploadedDocuments={uploadedDocuments}
            showMixed={false}
            variant="compact"
            title="Knowledge Source"
            className="flex-1"
          />

          <div className="flex items-center space-x-2 ml-6">
            <input
              type="checkbox"
              id="codeExecution"
              checked={useCodeExecution}
              onChange={(e) => setUseCodeExecution(e.target.checked)}
              className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
            />
            <label htmlFor="codeExecution" className="text-sm font-medium flex items-center text-gray-700">
              <Code className="w-4 h-4 mr-2 text-indigo-500" />
              Code Execution
            </label>
          </div>
        </div>
      </div>
